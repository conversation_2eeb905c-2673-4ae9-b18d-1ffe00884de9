#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ping脚本 - 用于ping ***********
"""

import subprocess
import sys
import platform
import time

def ping_host(host="***********", count=4):
    """
    Ping指定的主机
    
    Args:
        host (str): 要ping的主机地址，默认为***********
        count (int): ping的次数，默认为4次
    
    Returns:
        bool: ping成功返回True，失败返回False
    """
    try:
        # 根据操作系统选择ping命令参数
        if platform.system().lower() == "windows":
            # Windows系统使用 -n 参数
            cmd = ["ping", "-n", str(count), host]
        else:
            # Linux/Mac系统使用 -c 参数
            cmd = ["ping", "-c", str(count), host]
        
        print(f"正在ping {host}...")
        print("-" * 50)
        
        # 执行ping命令
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        # 输出ping结果
        print(result.stdout)
        
        if result.stderr:
            print("错误信息:")
            print(result.stderr)
        
        # 检查ping是否成功
        if result.returncode == 0:
            print(f"✅ Ping {host} 成功!")
            return True
        else:
            print(f"❌ Ping {host} 失败!")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ Ping {host} 超时!")
        return False
    except Exception as e:
        print(f"❌ 执行ping时发生错误: {e}")
        return False

def continuous_ping(host="***********", interval=1):
    """
    连续ping主机
    
    Args:
        host (str): 要ping的主机地址
        interval (int): ping间隔时间（秒）
    """
    print(f"开始连续ping {host}，按Ctrl+C停止...")
    print("-" * 50)
    
    success_count = 0
    fail_count = 0
    
    try:
        while True:
            # 根据操作系统选择ping命令参数
            if platform.system().lower() == "windows":
                cmd = ["ping", "-n", "1", host]
            else:
                cmd = ["ping", "-c", "1", host]
            
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
                
                if result.returncode == 0:
                    success_count += 1
                    print(f"✅ [{time.strftime('%H:%M:%S')}] Ping {host} 成功 (成功: {success_count}, 失败: {fail_count})")
                else:
                    fail_count += 1
                    print(f"❌ [{time.strftime('%H:%M:%S')}] Ping {host} 失败 (成功: {success_count}, 失败: {fail_count})")
                    
            except subprocess.TimeoutExpired:
                fail_count += 1
                print(f"⏰ [{time.strftime('%H:%M:%S')}] Ping {host} 超时 (成功: {success_count}, 失败: {fail_count})")
            
            time.sleep(interval)
            
    except KeyboardInterrupt:
        print(f"\n\n📊 统计结果:")
        print(f"成功: {success_count} 次")
        print(f"失败: {fail_count} 次")
        total = success_count + fail_count
        if total > 0:
            success_rate = (success_count / total) * 100
            print(f"成功率: {success_rate:.1f}%")

def main():
    """主函数"""
    print("🌐 Ping工具")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "-c" or sys.argv[1] == "--continuous":
            # 连续ping模式
            host = sys.argv[2] if len(sys.argv) > 2 else "***********"
            continuous_ping(host)
        elif sys.argv[1] == "-h" or sys.argv[1] == "--help":
            # 显示帮助信息
            print("使用方法:")
            print("  python ping_script.py                    # 普通ping ***********")
            print("  python ping_script.py -c [host]          # 连续ping指定主机")
            print("  python ping_script.py --continuous [host] # 连续ping指定主机")
            print("  python ping_script.py -h                 # 显示帮助信息")
            print("\n示例:")
            print("  python ping_script.py")
            print("  python ping_script.py -c *******")
            print("  python ping_script.py --continuous ***********")
        else:
            # ping指定主机
            host = sys.argv[1]
            ping_host(host)
    else:
        # 默认ping ***********
        ping_host("***********")

if __name__ == "__main__":
    main()
